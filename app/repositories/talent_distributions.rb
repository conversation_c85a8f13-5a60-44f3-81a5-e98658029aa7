# frozen_string_literal: true

class TalentDistributions < ApplicationRepository
  sort_by :id, :asc

  private

  def default_scope
    User.all.select('id', 'name')
  end

  def join_attribute(id: nil, key: nil)
    return '' if id.blank? && key.blank?

    upa_name = ActiveRecord::Base.connection.quote_column_name("upa_#{id}") if id.present?
    upa_name ||= ActiveRecord::Base.connection.quote_column_name("upa_#{key}")

    select = <<~SQL.squish
      upa.user_id,
      pa.partner_id,
      upa.string_value,
      upa.float_value
    SQL

    where = 'upa.discarded_at IS NULL'
    where += ' AND pa.id = :id' if id.present?
    where += ' AND pa.key = :key' if key.present?

    joins = <<~SQL.squish
      LEFT JOIN (
        SELECT #{select}
        FROM user_partner_attributes upa
        JOIN partner_attributes pa
          ON pa.id = upa.partner_attribute_id
          AND pa.discarded_at IS NULL
        WHERE #{where}
      ) #{upa_name}
        ON #{upa_name}.user_id = users.id
        AND #{upa_name}.partner_id = users.partner_id
    SQL

    ActiveRecord::Base.sanitize_sql [joins, { id:, key: }.compact]
  end

  def include_employee_id
    joins = join_attribute(key: 'employee_id')
    column = 'upa_employee_id.string_value AS employee_id'
    @scope.joins(joins).select(column)
  end

  def include_role
    joins = join_attribute(key: 'role')
    column = 'upa_role.string_value AS employee_role'
    @scope.joins(joins).select(column)
  end

  def filter_by_partner_id(partner_id)
    @scope.where(partner_id:)
  end

  def filter_by_not_role(role)
    @scope.where.not('users.role = ?', role)
  end

  def filter_by_x_variable_id(id)
    joins = join_attribute(id:)
    table = ActiveRecord::Base.connection.quote_column_name("upa_#{id}")
    column = "#{table}.float_value AS x_value"
    where = "#{table}.float_value IS NOT NULL"
    @scope.joins(joins).select(column).where(where)
  end

  def filter_by_y_variable_id(id)
    joins = join_attribute(id:)
    table = ActiveRecord::Base.connection.quote_column_name("upa_#{id}")
    column = "#{table}.float_value AS y_value"
    where = "#{table}.float_value IS NOT NULL"
    @scope.joins(joins).select(column).where(where)
  end

  def filter_by_job_level(job_level)
    return @scope if job_level.blank?

    joins = join_attribute(key: 'job_level')
    @scope.joins(joins).where('upa_job_level.string_value IN (?)', Array(job_level))
  end

  def filter_by_department(department)
    return @scope if department.blank?

    applied_values = department['applied_values'] if department.is_a?(Hash)
    applied_values ||= department if department.is_a?(Array)

    filter_type = department['filter_type'] if department.is_a?(Hash)
    filter_type ||= 'inclusion' if department.is_a?(Array)

    where_clause = 'upa_department.string_value NOT IN (?)' if filter_type == 'exclusion'
    where_clause ||= 'upa_department.string_value IN (?)'

    joins = join_attribute(key: 'department')
    @scope.joins(joins).where(where_clause, Array(applied_values).compact)
  end
end
